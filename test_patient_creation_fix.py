#!/usr/bin/env python3
"""
Test script to verify the patient creation fixes
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:5001"
LOGIN_URL = f"{BASE_URL}/api/auth/login"
PATIENT_URL = f"{BASE_URL}/api/patients"

def login():
    """Login and get authentication token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(LOGIN_URL, json=login_data)
    if response.status_code == 200:
        data = response.json()
        return data['token']
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        return None

def test_patient_creation(token):
    """Test patient creation with various scenarios"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("=== Testing Patient Creation Fixes ===\n")
    
    # Test 1: Valid patient data
    print("Test 1: Valid patient data")
    valid_patient = {
        "first_name": "<PERSON>",
        "last_name": "Doe",
        "gender": "Male",
        "date_of_birth": "1990-01-01",
        "phone": "**********",
        "email": "<EMAIL>",
        "address": "123 Main St",
        "city": "Chennai",
        "state": "Tamil Nadu",
        "postal_code": "600001",
        "tenant_id": 1
    }
    
    response = requests.post(PATIENT_URL, json=valid_patient, headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()
    
    # Test 2: Invalid phone number (not 10 digits)
    print("Test 2: Invalid phone number (not 10 digits)")
    invalid_phone_patient = {
        "first_name": "Jane",
        "last_name": "Smith",
        "gender": "Female",
        "date_of_birth": "1985-05-15",
        "phone": "12345",  # Invalid - not 10 digits
        "email": "<EMAIL>",
        "tenant_id": 1
    }
    
    response = requests.post(PATIENT_URL, json=invalid_phone_patient, headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()
    
    # Test 3: Invalid postal code (not 6 digits)
    print("Test 3: Invalid postal code (not 6 digits)")
    invalid_postal_patient = {
        "first_name": "Bob",
        "last_name": "Johnson",
        "gender": "Male",
        "date_of_birth": "1975-12-20",
        "phone": "**********",
        "postal_code": "12345",  # Invalid - not 6 digits
        "tenant_id": 1
    }
    
    response = requests.post(PATIENT_URL, json=invalid_postal_patient, headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()
    
    # Test 4: Duplicate patient (same name in same franchise)
    print("Test 4: Duplicate patient (same name in same franchise)")
    duplicate_patient = {
        "first_name": "John",  # Same as Test 1
        "last_name": "Doe",    # Same as Test 1
        "gender": "Male",
        "date_of_birth": "1990-01-01",
        "phone": "**********",  # Different phone
        "tenant_id": 1  # Same franchise
    }
    
    response = requests.post(PATIENT_URL, json=duplicate_patient, headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()
    
    # Test 5: Missing required field
    print("Test 5: Missing required field")
    missing_field_patient = {
        "first_name": "Alice",
        "last_name": "Brown",
        "gender": "Female",
        # Missing date_of_birth
        "phone": "**********",
        "tenant_id": 1
    }
    
    response = requests.post(PATIENT_URL, json=missing_field_patient, headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def main():
    """Main test function"""
    print("Starting Patient Creation API Tests...\n")
    
    # Login
    token = login()
    if not token:
        print("Failed to login. Exiting.")
        return
    
    print("Login successful!\n")
    
    # Run tests
    test_patient_creation(token)
    
    print("=== Test Complete ===")

if __name__ == "__main__":
    main()
